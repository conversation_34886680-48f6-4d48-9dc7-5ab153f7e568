from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

playlists_bp = Blueprint('playlists_bp', __name__)

# In api/playlists.py

# In api/playlists.py

@playlists_bp.route('/<playlist_id>', methods=['GET'])
@jwt_required()
def get_playlist_details(playlist_id):
    db = playlists_bp.db
    playlists_collection = db.playlists
    
    try:
        # THE FIX: First, find the playlist to ensure it exists.
        playlist = playlists_collection.find_one({'_id': ObjectId(playlist_id)})

        if not playlist:
            return jsonify({"msg": "Playlist not found"}), 404

        # If the playlist exists but is empty, we can return it as is.
        if not playlist.get('items'):
            playlist['populated_items'] = []
            return Response(json_util.dumps(playlist), mimetype='application/json')
        
        # If it has items, proceed with the aggregation to populate them.
        pipeline = [
            {'$match': {'_id': ObjectId(playlist_id)}},
            {'$unwind': '$items'},
            {
                '$lookup': {
                    'from': 'posts',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_post'
                }
            },
            {
                '$lookup': {
                    'from': 'places',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_place'
                }
            },
            {
                '$addFields': {
                    'populated_item': {'$ifNull': [{'$arrayElemAt': ['$populated_post', 0]}, {'$arrayElemAt': ['$populated_place', 0]}]}
                }
            },
            {
                '$group': {
                    '_id': '$_id',
                    'name': {'$first': '$name'},
                    'owner_id': {'$first': '$owner_id'},
                    'is_private': {'$first': '$is_private'},
                    'items': {'$push': '$items'},
                    'populated_items': {'$push': '$populated_item'}
                }
            }
        ]
        
        result = list(playlists_collection.aggregate(pipeline))
        
        return Response(json_util.dumps(result[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500



    # NEW: Route to get all playlists for the current user
@playlists_bp.route('/', methods=['GET'])
@jwt_required()
def get_my_playlists():
    db = playlists_bp.db
    playlists_collection = db.playlists
    current_user_id = get_jwt_identity()

    try:
        user_playlists = list(playlists_collection.find({
            "owner_id": ObjectId(current_user_id)
        }))
        return Response(
            json_util.dumps(user_playlists),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    



# @playlists_bp.route('/<playlist_id>', methods=['GET'])
# @jwt_required()
# def get_playlist_details(playlist_id):
    db = playlists_bp.db
    playlists_collection = db.playlists

    pipeline = [
        { '$match': { '_id': ObjectId(playlist_id) } },
        {
            '$lookup': {
                'from': 'posts', # Assuming items are posts for now
                'localField': 'items.item_id',
                'foreignField': '_id',
                'as': 'populated_items'
            }
        }
    ]

    try:
        playlist_details = list(playlists_collection.aggregate(pipeline))
        if not playlist_details:
            return jsonify({"msg": "Playlist not found"}), 404

        return Response(json_util.dumps(playlist_details[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500