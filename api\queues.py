import json
from flask import Response, current_app, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

queues_bp = Blueprint('queues_bp', __name__)

@queues_bp.route('/create', methods=['POST'])
@jwt_required()
def create_queue():
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data or not data.get('name'):
        return jsonify({"msg": "Queue name is required"}), 400

    new_queue = {
        "name": data.get('name'),
        "is_private": data.get('is_private', True),
        "owner_id": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "items": [] # Renamed from 'locations' to be consistent
    }

    try:
        queues_collection.insert_one(new_queue)
        return jsonify({"msg": "Queue created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# FIX: Renamed function to get_my_queues
@queues_bp.route('/', methods=['GET'])
@jwt_required()
def get_my_queues():
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()

    try:
        user_queues = list(queues_collection.find({
            "owner_id": ObjectId(current_user_id)
        }))
        return Response(
            json_util.dumps(user_queues),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    





@queues_bp.route('/<queue_id>', methods=['GET'])
@jwt_required()
def get_queue_details(queue_id): # FIX: Renamed function
    db = queues_bp.db
    queues_collection = db.queues # FIX: Added this line
    

    try:
        # This pipeline finds the playlist and then "looks up" the full
        # documents for the items (posts and places) it contains.
        pipeline = [
            {'$match': {'_id': ObjectId(queue_id)}},
            {'$unwind': '$items'},
            {
                '$lookup': {
                    'from': 'posts',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_post'
                }
            },
            {
                '$lookup': {
                    'from': 'places',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_place'
                }
            },
            {
                '$addFields': {
                    'populated_item': {'$ifNull': [{'$arrayElemAt': ['$populated_post', 0]}, {'$arrayElemAt': ['$populated_place', 0]}]}
                }
            },
            {
                '$group': {
                    '_id': '$_id',
                    'name': {'$first': '$name'},
                    'owner_id': {'$first': '$owner_id'},
                    'is_private': {'$first': '$is_private'},
                    'items': {'$push': '$items'},
                    'populated_items': {'$push': '$populated_item'}
                }
            }
        ]

        result = list(queues_collection.aggregate(pipeline))

        if not result:
            return jsonify({"msg": "Queue  not found"}), 404

        return Response(json_util.dumps(result[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


@queues_bp.route('/<queue_id>/plan', methods=['POST'])
@jwt_required()
def plan_queue_route(queue_id):
    """
    Uses an OpenAI agent to create an optimized itinerary for the places in a queue.
    """
    db = queues_bp.db
    queues_collection = db.queues
    openai_client = aopenai_client # Access the client from the app context

    # THE FIX: First, find the playlist to ensure it exists.
    queue = queues_collection.find_one({'_id': ObjectId(queue_id)})

    if not queue:
        return jsonify({"msg": "Queue not found"}), 404


    # First, we fetch the queue details using the same logic as get_queue_details
    # (This can be refactored into a helper function later)
    pipeline = [
        {'$match': {'_id': ObjectId(queue_id)}},
        {'$unwind': '$items'},
        {'$lookup': {'from': 'places', 'localField': 'items.item_id', 'foreignField': '_id', 'as': 'populated_place'}},
        {'$addFields': {'populated_item': {'$arrayElemAt': ['$populated_place', 0]}}},
        {'$group': {'_id': '$_id', 'name': {'$first': '$name'}, 'populated_items': {'$push': '$populated_item'}}}
    ]
    result = list(db.queues.aggregate(pipeline))
    if not result:
        return jsonify({"msg": "Queue not found"}), 404

    places = result[0]['populated_items']
    if not places:
        return jsonify({"msg": "This queue is empty, nothing to plan."}), 400

    # --- Agentic Workflow ---
    # 1. Format the places data for the AI prompt
    places_text = ""
    for i, place in enumerate(places):
        # Assuming places have 'opening_hours' and 'category' fields
        opening_hours = place.get('opening_hours', 'Not specified')
        category = place.get('category', 'General')
        places_text += f"{i+1}. {place.get('name')} (Category: {category}, Hours: {opening_hours})\n"

    # 2. Create the prompt for the OpenAI agent
    prompt = f"""
    You are a tour guide for Accra, Ghana. Given the following list of places, create an optimized one-day itinerary.
    Start the plan from 9:00 AM on Saturday, August 9, 2025.
    Consider logical factors like opening times and the type of place (e.g., a coffee shop in the morning, a restaurant for lunch, a nightclub in the evening).
    The user is located in Accra.

    Here is the list of places:
    {places_text}

    Your response must be in two parts:
    1. "plan": A summarized, step-by-step trip plan with timings and brief reasons for the order.
    2. "order": A comma-separated list of the original numbers of the places in the new, optimized order (e.g., "3,1,2,4").

    Format your response as a JSON object.
    """

    try:
        # 3. Call the OpenAI API
        completion = openai_client.chat.completions.create(
            model="gpt-4-turbo",
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": "You are a helpful tour guide assistant that provides responses in JSON format."},
                {"role": "user", "content": prompt}
            ]
        )

        response_data = json.loads(completion.choices[0].message.content)

        # 4. Reorder the original places list based on the AI's response
        optimized_order_indices = [int(i.strip()) - 1 for i in response_data.get('order', '').split(',')]
        ordered_places = [places[i] for i in optimized_order_indices if i < len(places)]

        return jsonify({
            "trip_plan": response_data.get('plan'),
            "ordered_places": json.loads(json_util.dumps(ordered_places))
        }), 200

    except Exception as e:
        return jsonify({"msg": "Failed to generate trip plan from AI", "error": str(e)}), 500