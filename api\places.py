import json
import os
from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
from werkzeug.utils import secure_filename
import uuid

places_bp = Blueprint('places_bp', __name__)

def get_db_collections(db):
    places_collection = db.places
    users_collection = db.users
    return places_collection, users_collection

@places_bp.route('/', methods=['GET', 'POST'])
@jwt_required()
def handle_places():
    if request.method == 'POST':
        return add_place()
    if request.method == 'GET':
        return get_all_places()

def add_place():
    db = places_bp.db
    places_collection, users_collection = get_db_collections(db)
    current_user_id = get_jwt_identity()
    data = request.form

    if not data or not data.get('name') or not data.get('location'):
        return jsonify({"msg": "Missing required fields: name, location"}), 400
    
    # --- Image Handling ---
    uploaded_photo_paths = []
    if 'images' in request.files:
        images = request.files.getlist('images')
        for image in images:
            if image.filename != '':
                filename = secure_filename(image.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads' 
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                image_path = os.path.join(upload_folder, unique_filename)
                
                image.save(image_path)
                uploaded_photo_paths.append(image_path)

    # --- Gamification Points ---
    points_awarded = 0
    if data.get('name'): points_awarded += 10
    if data.get('category'): points_awarded += 10
    if data.get('review'): points_awarded += 5
    if data.get('rating'): points_awarded += 5
    # FIX: Correctly count points for uploaded photos
    if uploaded_photo_paths: points_awarded += len(uploaded_photo_paths) * 2

    location_data = json.loads(data.get('location'))

    # --- Document Creation ---
    new_place = {
        "name": data.get('name'),
        "category": data.get('category'),
        "location": {
            "type": "Point",
            "coordinates": [float(location_data['longitude']), float(location_data['latitude'])]
        },
        "photos": uploaded_photo_paths, # FIX: The correct list is now the only one here
        "created_by": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "reviews": [],
        "overall_rating": 0,
    }

    if data.get('review') or data.get('rating'):
        initial_review = {
            "user_id": ObjectId(current_user_id),
            "rating": float(data.get('rating', 0)),
            "review_text": data.get('review', ""),
            "created_at": datetime.datetime.now(datetime.timezone.utc)
        }
        new_place["reviews"].append(initial_review)
        new_place["overall_rating"] = float(data.get('rating', 0))

    try:
        places_collection.insert_one(new_place)
        users_collection.update_one(
            {'_id': ObjectId(current_user_id)},
            {'$inc': {'points': points_awarded}}
        )
        return jsonify({"msg": f"Place added! You earned {points_awarded} points."}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    




def get_all_places():
    db = places_bp.db
    places_collection, _ = get_db_collections(db)
    try:
        places = list(places_collection.find({}))
        # This directly returns the JSON string with the correct content type
        return Response(
            json_util.dumps(places),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500