from flask import Response, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import json

users_bp = Blueprint('users_bp', __name__)

@users_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_my_profile():
    """Fetches the profile data for the currently logged-in user."""
    db = users_bp.db
    users_collection = db.users
    current_user_id = get_jwt_identity()

    try:
        user_profile = users_collection.find_one(
            {'_id': ObjectId(current_user_id)},
            # Projection to exclude sensitive fields like password
            {'password_hash': 0}
        )
        if not user_profile:
            return jsonify({"msg": "User not found"}), 404

        # Use json_util to handle BSON types like ObjectId
        return json.loads(json_util.dumps(user_profile)), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    
# In api/users.py

@users_bp.route('/my-contributions', methods=['GET'])
@jwt_required()
def get_my_contributions():
    """Fetches a combined list of a user's posts and created places."""
    db = users_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        # Fetch posts created by the user
        posts_pipeline = [
            {'$match': {'author_id': current_user_id}},
            {'$addFields': {'contribution_type': 'post'}}, # Add a type field
            {'$sort': {'created_at': -1}}
        ]
        user_posts = list(db.posts.aggregate(posts_pipeline))

        # Fetch places created by the user
        places_pipeline = [
            {'$match': {'created_by': current_user_id}},
            {'$addFields': {'contribution_type': 'place'}}, # Add a type field
            {'$sort': {'created_at': -1}}
        ]
        user_places = list(db.places.aggregate(places_pipeline))

        # Combine and sort all contributions by date
        all_contributions = sorted(
            user_posts + user_places,
            key=lambda x: x['created_at'],
            reverse=True
        )

        return Response(json_util.dumps(all_contributions), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500