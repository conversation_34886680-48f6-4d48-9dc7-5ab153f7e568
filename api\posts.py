import os
import uuid
from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
from werkzeug.utils import secure_filename
from PIL import Image


posts_bp = Blueprint('posts_bp', __name__)

# Define allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'mov', 'mp3', 'wav', 'm4a'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@posts_bp.route('/', methods=['GET', 'POST'])
@jwt_required()
def handle_posts():
    if request.method == 'POST':
        # This will be used by a future version of createPost
        pass 
    if request.method == 'GET':
        return get_all_posts()


@posts_bp.route('/create', methods=['POST'])
@jwt_required()
def create_post():
    db = posts_bp.db
    posts_collection = db.posts
    current_user_id = get_jwt_identity()

    if 'text_content' not in request.form:
        return jsonify({"msg": "Text content is required"}), 400

    text_content = request.form.get('text_content')
    tagged_place_id = request.form.get('tagged_place_id')

    media_items = []
    if 'media' in request.files:
        media_files = request.files.getlist('media')
        for file in media_files:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                media_type = get_media_type(filename)
                
                # Use original extension for non-image files
                file_extension = os.path.splitext(filename)[1] if media_type != 'image' else '.webp'
                unique_filename = f"{uuid.uuid4()}{file_extension}"
                
                upload_folder = 'uploads/posts'
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                file_path = os.path.join(upload_folder, unique_filename)

                # --- THE FIX: Differentiated Media Processing ---
                aspect_ratio = 16/9 # Default for videos/audio
                
                if media_type == 'image':
                    try:
                        with Image.open(file) as img:
                            max_width = 1080
                            if img.width > max_width:
                                height = int((max_width / img.width) * img.height)
                                img = img.resize((max_width, height), Image.Resampling.LANCZOS)
                            
                            img.save(file_path, 'webp', quality=85)
                            width, height = img.size
                            aspect_ratio = width / height if height > 0 else 1.0
                    except Exception as e:
                        print(f"Failed to process image: {e}")
                        continue # Skip this file if processing fails
                
                elif media_type in ['video', 'audio']:
                    # For now, just save the original file.
                    # This is where FFmpeg processing will be added later.
                    file.save(file_path)
                    # We can't get video aspect ratio easily without a library like ffmpeg-python
                    # so we use a default.
                
                else:
                    continue # Skip unsupported file types

                media_items.append({
                    "path": file_path,
                    "type": media_type,
                    "aspect_ratio": aspect_ratio
                })

    new_post = {
        "author_id": ObjectId(current_user_id),
        "text_content": text_content,
        "media": media_items,
        "tagged_place_id": ObjectId(tagged_place_id) if tagged_place_id else None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "likes": [],
        "comments": []
    }

    try:
        posts_collection.insert_one(new_post)
        return jsonify({"msg": "Post created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@posts_bp.route('/my-posts', methods=['GET'])
@jwt_required()
def get_my_posts():
    db = posts_bp.db
    posts_collection = db.posts
    current_user_id = get_jwt_identity()

    try:
        user_posts = list(posts_collection.find({
            "author_id": ObjectId(current_user_id)
        }))
        return Response(json_util.dumps(user_posts), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    

def get_all_posts():
    db = posts_bp.db
    posts_collection = db.posts
    try:
        pipeline = [
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'author_id',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            { '$unwind': '$author_details' },
            { '$sort': {'created_at': -1} },
            {
                '$project': {
                    # THE FIX: Ensure we are not sending the old 'media_paths'
                    # and that the new 'media' field is included.
                    'author_details.password_hash': 0,
                    'author_details.email': 0,
                    'media_paths': 0 # Explicitly exclude the old field if it exists
                }
            }
        ]
        posts = list(posts_collection.aggregate(pipeline))
        return Response(
            json_util.dumps(posts),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500 

def get_media_type(filename):
    """Determines if a file is an image, video, or audio."""
    ext = filename.rsplit('.', 1)[1].lower()
    if ext in ['png', 'jpg', 'jpeg', 'gif']:
        return 'image'
    if ext in ['mp4', 'mov', 'avi']:
        return 'video'
    if ext in ['mp3', 'wav', 'm4a']:
        return 'audio'
    return 'unknown'